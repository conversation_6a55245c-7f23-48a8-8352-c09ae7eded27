{"$schema": "https://schema.tauri.app/config/2", "productName": "whatdid", "version": "0.1.0", "identifier": "com.yuvalshavit.whatdid", "build": {"beforeDevCommand": "dx serve --port 1420", "devUrl": "http://localhost:1420", "beforeBuildCommand": "dx bundle --release", "frontendDist": "../dist/public"}, "app": {"withGlobalTauri": true, "windows": [{"title": "whatdid", "width": 800, "height": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}